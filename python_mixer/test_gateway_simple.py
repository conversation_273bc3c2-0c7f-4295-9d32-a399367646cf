#!/usr/bin/env python3
"""
🧪 SIMPLE GATEWAY TEST
Prosty test Communication Gateway bez circular imports
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add current directory to path
sys.path.append('.')

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_gateway():
    """Test Communication Gateway"""
    try:
        # Direct import
        from core.integration.unified_communication_gateway import UnifiedCommunicationGateway, CommunicationConfig
        
        logger.info("🧪 Testing Communication Gateway...")
        
        # Create config
        config = CommunicationConfig(
            gospine_rest_url="http://localhost:8080",
            gospine_grpc_url="localhost:9000",
            redis_url="redis://localhost:6379"
        )
        
        # Create gateway
        gateway = UnifiedCommunicationGateway(config)
        
        # Initialize
        success = await gateway.initialize()
        logger.info(f"Gateway initialization: {'✅ Success' if success else '❌ Failed'}")
        
        # Test health check (will fail if <PERSON><PERSON><PERSON> not running, but that's OK)
        try:
            result = await gateway.send_request("health_check")
            logger.info(f"Health check result: {result}")
        except Exception as e:
            logger.info(f"Health check failed (expected if GoSpine not running): {e}")
        
        # Get metrics
        metrics = await gateway.get_metrics()
        logger.info(f"Gateway metrics: {metrics}")
        
        # Close
        await gateway.close()
        
        logger.info("✅ Gateway test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Gateway test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_config():
    """Test Enhanced Config"""
    try:
        from core.config.enhanced_config import ConfigurationManager
        
        logger.info("🧪 Testing Enhanced Config...")
        
        # Create config manager
        config_manager = ConfigurationManager()
        
        # Load config
        config = await config_manager.load_config()
        logger.info(f"Config loaded: {config.company_name}")
        
        # Test service endpoint
        gospine_endpoint = config_manager.get_service_endpoint("gospine")
        logger.info(f"GoSpine endpoint: {gospine_endpoint.url if gospine_endpoint else 'None'}")
        
        # Close
        await config_manager.close()
        
        logger.info("✅ Config test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    logger.info("🚀 Starting simple gateway tests...")
    
    # Test config first
    config_success = await test_config()
    
    # Test gateway
    gateway_success = await test_gateway()
    
    # Summary
    if config_success and gateway_success:
        logger.info("🎉 All tests passed!")
        return True
    else:
        logger.error("❌ Some tests failed")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
