{"test_start_time": "2025-05-31T03:36:10.894809", "test_timestamp": "2025-05-31T03:36:10.894938", "endpoint_connectivity": {"gospine_health": {"status": "failed", "error": "Cannot connect to host localhost:8080 ssl:default [Connect call failed ('127.0.0.1', 8080)]", "url": "http://localhost:8080/health"}, "gospine_dashboard": {"status": "failed", "error": "Cannot connect to host localhost:8080 ssl:default [Connect call failed ('127.0.0.1', 8080)]", "url": "http://localhost:8080"}, "gospine_customers": {"status": "failed", "error": "Cannot connect to host localhost:8080 ssl:default [Connect call failed ('127.0.0.1', 8080)]", "url": "http://localhost:8080/api/v1/crm/customers"}, "gospine_transcription": {"status": "failed", "error": "Cannot connect to host localhost:8080 ssl:default [Connect call failed ('127.0.0.1', 8080)]", "url": "http://localhost:8080/api/v1/transcription/submit"}, "document_processor": {"status": "failed", "error": "Cannot connect to host localhost:7861 ssl:default [Connect call failed ('127.0.0.1', 7861)]", "url": "http://localhost:7861"}, "knowledge_graph": {"status": "failed", "error": "Cannot connect to host localhost:7860 ssl:default [Connect call failed ('127.0.0.1', 7860)]", "url": "http://localhost:7860"}, "main_interface": {"status": "failed", "error": "Cannot connect to host localhost:7862 ssl:default [Connect call failed ('127.0.0.1', 7862)]", "url": "http://localhost:7862"}, "graphiti_api": {"status": "failed", "error": "Cannot connect to host localhost:8001 ssl:default [Connect call failed ('127.0.0.1', 8001)]", "url": "http://localhost:8001/docs"}, "lm_studio": {"status": "success", "status_code": 200, "response_time": 0.002791166305541992, "url": "http://*************:1234/v1/models"}}, "database_connectivity": {"postgresql": {"status": "failed", "error": "password authentication failed for user \"postgres\""}, "redis": {"status": "success", "version": "7.4.3"}}, "ai_services": {"lm_studio": {"status": "success", "models_count": 4, "available": true}}, "communication_gateway": {"status": "failed", "error": "duplicate base class TimeoutError", "gateway_initialized": false}, "integration_orchestrator": {"status": "failed", "error": "duplicate base class TimeoutError"}, "email_processing": {"status": "failed", "error": "duplicate base class TimeoutError", "test_email": {"email_id": "test_email_001", "sender_email": "<EMAIL>", "sender_name": "Test Customer", "subject": "<PERSON><PERSON><PERSON> - pilne", "body": "<PERSON><PERSON><PERSON>, mam a<PERSON>ę klimatyzacji LG w mieszkaniu. Proszę o szybki kontakt.", "timestamp": "2025-05-31T03:36:11.230230", "phone": "+48123456789"}}, "transcription_processing": {"status": "failed", "error": "duplicate base class TimeoutError", "test_transcription": {"email_id": "test_transcription_001", "file_path": "/test/audio.m4a", "transcript": "<PERSON><PERSON><PERSON>, dzwonię w sprawie awarii klimatyzacji. Urządzenie LG przestało działać wczoraj wieczorem. Proszę o pilny kontakt.", "confidence": 0.95, "language": "pl", "processing_time": 15.5, "metadata": {"model": "nvidia_nemo_fastconformer", "audio_duration": 30.2}}}, "summary": {"total_tests": 16, "passed_tests": 3, "failed_tests": 13, "success_rate": 18.75, "overall_status": "failed"}, "test_duration": 0.338497}