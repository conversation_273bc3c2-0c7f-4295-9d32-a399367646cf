#!/usr/bin/env python3
"""
⚙️ ENHANCED CONFIGURATION MANAGEMENT
Centralne zarządzanie konfiguracją dla zunifikowanego systemu Python Mixer + GoSpine
"""

import os
import yaml
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict, field
from pydantic import Field, validator
from pydantic_settings import BaseSettings
import redis.asyncio as redis
from cryptography.fernet import Fernet

logger = logging.getLogger(__name__)

@dataclass
class ServiceEndpoint:
    """Definicja endpointu serwisu"""
    host: str
    port: int
    protocol: str = "http"
    path: str = ""
    timeout: int = 30
    
    @property
    def url(self) -> str:
        """Pełny URL endpointu"""
        base_url = f"{self.protocol}://{self.host}:{self.port}"
        return f"{base_url}{self.path}" if self.path else base_url

@dataclass
class DatabaseConfig:
    """Konfiguracja bazy danych"""
    host: str
    port: int
    database: str
    username: str
    password: str
    pool_size: int = 10
    max_overflow: int = 20
    
    @property
    def connection_url(self) -> str:
        """URL połączenia z bazą"""
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"

@dataclass
class RedisConfig:
    """Konfiguracja Redis"""
    host: str = "localhost"
    port: int = 6379
    password: Optional[str] = None
    db: int = 0
    max_connections: int = 10
    
    @property
    def connection_url(self) -> str:
        """URL połączenia z Redis"""
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
        return f"redis://{self.host}:{self.port}/{self.db}"

@dataclass
class AIServiceConfig:
    """Konfiguracja serwisów AI"""
    lm_studio: ServiceEndpoint
    nvidia_stt: Optional[ServiceEndpoint] = None
    elevenlabs: Optional[ServiceEndpoint] = None
    
    def __post_init__(self):
        if isinstance(self.lm_studio, dict):
            self.lm_studio = ServiceEndpoint(**self.lm_studio)
        if self.nvidia_stt and isinstance(self.nvidia_stt, dict):
            self.nvidia_stt = ServiceEndpoint(**self.nvidia_stt)
        if self.elevenlabs and isinstance(self.elevenlabs, dict):
            self.elevenlabs = ServiceEndpoint(**self.elevenlabs)

@dataclass
class SecurityConfig:
    """Konfiguracja bezpieczeństwa"""
    encryption_key: Optional[str] = None
    jwt_secret: Optional[str] = None
    api_keys: Dict[str, str] = field(default_factory=dict)
    rate_limits: Dict[str, int] = field(default_factory=lambda: {
        "default": 100,
        "transcription": 10,
        "email_processing": 50
    })
    
    def __post_init__(self):
        if not self.encryption_key:
            self.encryption_key = Fernet.generate_key().decode()
        if not self.jwt_secret:
            self.jwt_secret = os.urandom(32).hex()

@dataclass
class EnhancedSystemConfig:
    """Główna konfiguracja systemu"""
    # Core services
    gospine: ServiceEndpoint
    database: DatabaseConfig
    redis: RedisConfig
    ai_services: AIServiceConfig
    security: SecurityConfig
    
    # Application settings
    debug: bool = False
    log_level: str = "INFO"
    environment: str = "development"
    
    # Business settings
    company_name: str = "Fulmark HVAC"
    company_email: str = "<EMAIL>"
    timezone: str = "Europe/Warsaw"
    
    # Processing settings
    max_email_batch_size: int = 50
    email_check_interval: int = 300
    transcription_timeout: int = 300
    document_processing_timeout: int = 120
    
    # Integration settings
    circuit_breaker_threshold: int = 5
    retry_attempts: int = 3
    cache_ttl: int = 300
    request_timeout: int = 30

    # Additional settings from YAML (flexible handling)
    email: Optional[Dict[str, Any]] = None
    storage: Optional[Dict[str, Any]] = None
    monitoring: Optional[Dict[str, Any]] = None
    services: Optional[Dict[str, Any]] = None
    features: Optional[Dict[str, Any]] = None
    performance: Optional[Dict[str, Any]] = None
    development: Optional[Dict[str, Any]] = None
    production: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        # Convert dicts to dataclasses
        if isinstance(self.gospine, dict):
            self.gospine = ServiceEndpoint(**self.gospine)
        if isinstance(self.database, dict):
            self.database = DatabaseConfig(**self.database)
        if isinstance(self.redis, dict):
            self.redis = RedisConfig(**self.redis)
        if isinstance(self.ai_services, dict):
            self.ai_services = AIServiceConfig(**self.ai_services)
        if isinstance(self.security, dict):
            self.security = SecurityConfig(**self.security)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EnhancedSystemConfig':
        """Create config from dict, filtering unknown fields"""
        # Get valid field names
        import inspect
        sig = inspect.signature(cls.__init__)
        valid_fields = set(sig.parameters.keys()) - {'self'}

        # Filter data to only include valid fields
        filtered_data = {k: v for k, v in data.items() if k in valid_fields}

        return cls(**filtered_data)

class ConfigurationManager:
    """⚙️ Manager konfiguracji"""
    
    def __init__(self, config_path: Optional[Path] = None):
        self.config_path = config_path or Path("config/enhanced_config.yaml")
        self.config: Optional[EnhancedSystemConfig] = None
        self.redis_client: Optional[redis.Redis] = None
        self._encryption_key: Optional[bytes] = None
    
    async def load_config(self) -> EnhancedSystemConfig:
        """📥 Załaduj konfigurację"""
        logger.info("📥 Ładowanie konfiguracji systemu...")
        
        # Load from file
        if self.config_path.exists():
            config_data = self._load_from_file()
        else:
            logger.warning("⚠️ Plik konfiguracji nie istnieje, używam domyślnej")
            config_data = self._get_default_config()
        
        # Override with environment variables
        config_data = self._override_with_env_vars(config_data)
        
        # Create config object
        self.config = EnhancedSystemConfig.from_dict(config_data)
        
        # Initialize encryption
        self._encryption_key = self.config.security.encryption_key.encode()
        
        # Initialize Redis client
        await self._initialize_redis()
        
        # Load secrets from Redis if available
        await self._load_secrets_from_redis()
        
        logger.info("✅ Konfiguracja załadowana pomyślnie")
        return self.config
    
    def _load_from_file(self) -> Dict[str, Any]:
        """📄 Załaduj z pliku"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                if self.config_path.suffix.lower() == '.yaml':
                    return yaml.safe_load(f)
                else:
                    return json.load(f)
        except Exception as e:
            logger.error(f"❌ Błąd ładowania konfiguracji z pliku: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """🔧 Domyślna konfiguracja"""
        return {
            "gospine": {
                "host": "localhost",
                "port": 8080,
                "protocol": "http"
            },
            "database": {
                "host": "**************",
                "port": 5432,
                "database": "hvac_crm",
                "username": "postgres",
                "password": "Blaeritipol1"
            },
            "redis": {
                "host": "localhost",
                "port": 6379
            },
            "ai_services": {
                "lm_studio": {
                    "host": "*************",
                    "port": 1234,
                    "protocol": "http",
                    "path": "/v1"
                }
            },
            "security": {}
        }
    
    def _override_with_env_vars(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """🌍 Override z zmiennymi środowiskowymi"""
        
        # Database overrides
        if "POSTGRES_HOST" in os.environ:
            config_data["database"]["host"] = os.environ["POSTGRES_HOST"]
        if "POSTGRES_PASSWORD" in os.environ:
            config_data["database"]["password"] = os.environ["POSTGRES_PASSWORD"]
        
        # Redis overrides
        if "REDIS_HOST" in os.environ:
            config_data["redis"]["host"] = os.environ["REDIS_HOST"]
        if "REDIS_PASSWORD" in os.environ:
            config_data["redis"]["password"] = os.environ["REDIS_PASSWORD"]
        
        # GoSpine overrides
        if "GOSPINE_HOST" in os.environ:
            config_data["gospine"]["host"] = os.environ["GOSPINE_HOST"]
        if "GOSPINE_PORT" in os.environ:
            config_data["gospine"]["port"] = int(os.environ["GOSPINE_PORT"])
        
        # AI Services overrides
        if "LM_STUDIO_HOST" in os.environ:
            config_data["ai_services"]["lm_studio"]["host"] = os.environ["LM_STUDIO_HOST"]
        
        # Security overrides
        if "ENCRYPTION_KEY" in os.environ:
            config_data["security"]["encryption_key"] = os.environ["ENCRYPTION_KEY"]
        if "JWT_SECRET" in os.environ:
            config_data["security"]["jwt_secret"] = os.environ["JWT_SECRET"]
        
        return config_data
    
    async def _initialize_redis(self):
        """🔴 Inicjalizuj Redis client"""
        try:
            self.redis_client = redis.from_url(self.config.redis.connection_url)
            await self.redis_client.ping()
            logger.info("✅ Redis client zainicjalizowany")
        except Exception as e:
            logger.warning(f"⚠️ Nie można połączyć z Redis: {e}")
            self.redis_client = None
    
    async def _load_secrets_from_redis(self):
        """🔐 Załaduj sekrety z Redis"""
        if not self.redis_client:
            return
        
        try:
            # Load encrypted secrets
            encrypted_secrets = await self.redis_client.get("system:secrets")
            if encrypted_secrets:
                fernet = Fernet(self._encryption_key)
                secrets_json = fernet.decrypt(encrypted_secrets.encode())
                secrets = json.loads(secrets_json)
                
                # Update config with secrets
                for key, value in secrets.items():
                    if hasattr(self.config.security, key):
                        setattr(self.config.security, key, value)
                
                logger.info("🔐 Sekrety załadowane z Redis")
        except Exception as e:
            logger.warning(f"⚠️ Nie można załadować sekretów z Redis: {e}")
    
    async def save_secrets_to_redis(self, secrets: Dict[str, str]):
        """💾 Zapisz sekrety do Redis"""
        if not self.redis_client or not self._encryption_key:
            return False
        
        try:
            fernet = Fernet(self._encryption_key)
            secrets_json = json.dumps(secrets)
            encrypted_secrets = fernet.encrypt(secrets_json.encode())
            
            await self.redis_client.set(
                "system:secrets", 
                encrypted_secrets.decode(),
                ex=86400  # 24 hours
            )
            
            logger.info("💾 Sekrety zapisane do Redis")
            return True
        except Exception as e:
            logger.error(f"❌ Błąd zapisywania sekretów: {e}")
            return False
    
    async def save_config_to_file(self):
        """💾 Zapisz konfigurację do pliku"""
        if not self.config:
            return False
        
        try:
            # Create config directory
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Convert to dict (without sensitive data)
            config_dict = asdict(self.config)
            
            # Remove sensitive data
            if "password" in config_dict.get("database", {}):
                config_dict["database"]["password"] = "***REDACTED***"
            if "api_keys" in config_dict.get("security", {}):
                config_dict["security"]["api_keys"] = {}
            if "encryption_key" in config_dict.get("security", {}):
                config_dict["security"]["encryption_key"] = "***REDACTED***"
            
            # Save to file
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"💾 Konfiguracja zapisana do {self.config_path}")
            return True
        except Exception as e:
            logger.error(f"❌ Błąd zapisywania konfiguracji: {e}")
            return False
    
    def get_service_endpoint(self, service_name: str) -> Optional[ServiceEndpoint]:
        """🔗 Pobierz endpoint serwisu"""
        if not self.config:
            return None
        
        if service_name == "gospine":
            return self.config.gospine
        elif service_name == "lm_studio":
            return self.config.ai_services.lm_studio
        elif service_name == "nvidia_stt":
            return self.config.ai_services.nvidia_stt
        elif service_name == "elevenlabs":
            return self.config.ai_services.elevenlabs
        
        return None
    
    async def update_config(self, updates: Dict[str, Any]):
        """🔄 Aktualizuj konfigurację"""
        if not self.config:
            return False
        
        try:
            # Apply updates
            for key, value in updates.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
            
            # Save to file
            await self.save_config_to_file()
            
            logger.info("🔄 Konfiguracja zaktualizowana")
            return True
        except Exception as e:
            logger.error(f"❌ Błąd aktualizacji konfiguracji: {e}")
            return False
    
    async def close(self):
        """🔒 Zamknij połączenia"""
        if self.redis_client:
            await self.redis_client.close()

# Singleton instance
_config_manager: Optional[ConfigurationManager] = None

async def get_config_manager() -> ConfigurationManager:
    """Pobierz singleton instance config manager"""
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ConfigurationManager()
        await _config_manager.load_config()
    
    return _config_manager

async def get_config() -> EnhancedSystemConfig:
    """Pobierz aktualną konfigurację"""
    manager = await get_config_manager()
    return manager.config
