#!/usr/bin/env python3
"""
🌐 UNIFIED COMMUNICATION GATEWAY
Centralny punkt komunikacji między Python Mixer a GoSpine
Implementuje wzorzec Gateway dla wszystkich interakcji między systemami
"""

import asyncio
import aiohttp
import grpc
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import redis.asyncio as redis
from contextlib import asynccontextmanager

# Import protobuf definitions (będą dodane)
# from api.hvac.v1 import hvac_pb2, hvac_pb2_grpc
# from api.ai.v1 import ai_pb2, ai_pb2_grpc

logger = logging.getLogger(__name__)

@dataclass
class CommunicationConfig:
    """Konfiguracja komunikacji"""
    gospine_rest_url: str = "http://localhost:8080"
    gospine_grpc_url: str = "localhost:9000"
    redis_url: str = "redis://localhost:6379"
    timeout: int = 30
    retry_attempts: int = 3
    circuit_breaker_threshold: int = 5

@dataclass
class ServiceEndpoint:
    """Definicja endpointu serwisu"""
    name: str
    url: str
    method: str
    timeout: int = 30
    requires_auth: bool = False

class CircuitBreaker:
    """Circuit Breaker pattern dla odporności"""
    
    def __init__(self, threshold: int = 5, timeout: int = 60):
        self.threshold = threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def can_execute(self) -> bool:
        """Sprawdź czy można wykonać żądanie"""
        if self.state == "CLOSED":
            return True
        elif self.state == "OPEN":
            if datetime.now().timestamp() - self.last_failure_time > self.timeout:
                self.state = "HALF_OPEN"
                return True
            return False
        else:  # HALF_OPEN
            return True
    
    def record_success(self):
        """Zapisz sukces"""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def record_failure(self):
        """Zapisz błąd"""
        self.failure_count += 1
        self.last_failure_time = datetime.now().timestamp()
        
        if self.failure_count >= self.threshold:
            self.state = "OPEN"

class UnifiedCommunicationGateway:
    """🌐 Centralny gateway komunikacji"""
    
    def __init__(self, config: CommunicationConfig = None):
        self.config = config or CommunicationConfig()
        
        # HTTP Session
        self.session: Optional[aiohttp.ClientSession] = None
        
        # gRPC Channels
        self.grpc_channels: Dict[str, grpc.aio.Channel] = {}
        
        # Redis Connection
        self.redis: Optional[redis.Redis] = None
        
        # Circuit Breakers
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        
        # Service Registry
        self.services = self._initialize_service_registry()
        
        # Metrics
        self.metrics = {
            "requests_sent": 0,
            "requests_successful": 0,
            "requests_failed": 0,
            "avg_response_time": 0.0,
            "circuit_breaker_trips": 0
        }
    
    def _initialize_service_registry(self) -> Dict[str, ServiceEndpoint]:
        """Inicjalizuj rejestr serwisów"""
        return {
            # GoSpine REST Endpoints
            "create_customer": ServiceEndpoint(
                name="create_customer",
                url=f"{self.config.gospine_rest_url}/api/v1/crm/customers",
                method="POST"
            ),
            "create_lead": ServiceEndpoint(
                name="create_lead", 
                url=f"{self.config.gospine_rest_url}/api/v1/crm/leads",
                method="POST"
            ),
            "submit_transcription": ServiceEndpoint(
                name="submit_transcription",
                url=f"{self.config.gospine_rest_url}/api/v1/transcription/submit",
                method="POST"
            ),
            "process_email": ServiceEndpoint(
                name="process_email",
                url=f"{self.config.gospine_rest_url}/api/v1/email/process", 
                method="POST"
            ),
            "get_equipment": ServiceEndpoint(
                name="get_equipment",
                url=f"{self.config.gospine_rest_url}/api/v1/crm/equipment",
                method="GET"
            ),
            "health_check": ServiceEndpoint(
                name="health_check",
                url=f"{self.config.gospine_rest_url}/health",
                method="GET",
                timeout=5
            )
        }
    
    async def initialize(self) -> bool:
        """🚀 Inicjalizacja gateway"""
        logger.info("🌐 Inicjalizacja Unified Communication Gateway...")
        
        try:
            # HTTP Session
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            # Redis Connection
            self.redis = redis.from_url(self.config.redis_url)
            await self.redis.ping()
            
            # gRPC Channels
            await self._initialize_grpc_channels()
            
            # Circuit Breakers
            for service_name in self.services.keys():
                self.circuit_breakers[service_name] = CircuitBreaker(
                    threshold=self.config.circuit_breaker_threshold
                )
            
            # Test connectivity
            health_ok = await self._test_connectivity()
            
            logger.info("✅ Unified Communication Gateway zainicjalizowany")
            return health_ok
            
        except Exception as e:
            logger.error(f"❌ Błąd inicjalizacji gateway: {e}")
            return False
    
    async def _initialize_grpc_channels(self):
        """Inicjalizuj kanały gRPC"""
        try:
            # Main gRPC channel
            channel = grpc.aio.insecure_channel(self.config.gospine_grpc_url)
            self.grpc_channels["main"] = channel
            
            # Test connection with timeout
            try:
                await asyncio.wait_for(channel.channel_ready(), timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning("⚠️ gRPC connection timeout - GoSpine may not be running")
                return
            logger.info("✅ gRPC channel połączony")
            
        except Exception as e:
            logger.warning(f"⚠️ Nie można połączyć z gRPC: {e}")
    
    async def _test_connectivity(self) -> bool:
        """Test połączenia z GoSpine"""
        try:
            result = await self.send_request("health_check")
            return result.get("success", False)
        except:
            logger.warning("⚠️ GoSpine health check failed")
            return False
    
    @asynccontextmanager
    async def _get_circuit_breaker(self, service_name: str):
        """Context manager dla Circuit Breaker"""
        breaker = self.circuit_breakers.get(service_name)
        
        if not breaker or not breaker.can_execute():
            raise Exception(f"Circuit breaker OPEN dla {service_name}")
        
        try:
            yield breaker
            breaker.record_success()
        except Exception as e:
            breaker.record_failure()
            if breaker.state == "OPEN":
                self.metrics["circuit_breaker_trips"] += 1
            raise e
    
    async def send_request(
        self, 
        service_name: str, 
        data: Dict[str, Any] = None,
        use_cache: bool = False,
        cache_ttl: int = 300
    ) -> Dict[str, Any]:
        """📤 Wyślij żądanie do GoSpine"""
        
        if service_name not in self.services:
            raise ValueError(f"Nieznany serwis: {service_name}")
        
        service = self.services[service_name]
        start_time = datetime.now()
        
        # Sprawdź cache
        if use_cache and self.redis:
            cache_key = f"gateway:{service_name}:{hash(str(data))}"
            cached = await self.redis.get(cache_key)
            if cached:
                return json.loads(cached)
        
        try:
            async with self._get_circuit_breaker(service_name) as breaker:
                
                # Wykonaj żądanie HTTP
                async with self.session.request(
                    service.method,
                    service.url,
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=service.timeout)
                ) as response:
                    
                    result = await response.json()
                    
                    # Zapisz w cache
                    if use_cache and self.redis and response.status == 200:
                        await self.redis.setex(
                            cache_key, 
                            cache_ttl, 
                            json.dumps(result)
                        )
                    
                    # Aktualizuj metryki
                    self._update_metrics(start_time, True)
                    
                    return {
                        "success": response.status == 200,
                        "status_code": response.status,
                        "data": result
                    }
        
        except Exception as e:
            self._update_metrics(start_time, False)
            logger.error(f"❌ Błąd żądania {service_name}: {e}")
            
            return {
                "success": False,
                "error": str(e),
                "service": service_name
            }
    
    def _update_metrics(self, start_time: datetime, success: bool):
        """Aktualizuj metryki"""
        self.metrics["requests_sent"] += 1
        
        if success:
            self.metrics["requests_successful"] += 1
        else:
            self.metrics["requests_failed"] += 1
        
        # Oblicz średni czas odpowiedzi
        response_time = (datetime.now() - start_time).total_seconds()
        current_avg = self.metrics["avg_response_time"]
        total_requests = self.metrics["requests_sent"]
        
        self.metrics["avg_response_time"] = (
            (current_avg * (total_requests - 1) + response_time) / total_requests
        )
    
    async def send_transcription_data(self, transcription_data: Dict[str, Any]) -> Dict[str, Any]:
        """📝 Wyślij dane transkrypcji"""
        return await self.send_request("submit_transcription", transcription_data)
    
    async def create_customer_from_email(self, email_data: Dict[str, Any]) -> Dict[str, Any]:
        """👤 Utwórz klienta z danych email"""
        customer_data = {
            "name": email_data.get("sender_name", ""),
            "email": email_data.get("sender_email", ""),
            "phone": email_data.get("phone", ""),
            "source": "email_processing",
            "metadata": email_data
        }
        
        return await self.send_request("create_customer", customer_data)
    
    async def create_lead_from_transcription(self, transcription_data: Dict[str, Any]) -> Dict[str, Any]:
        """🎯 Utwórz lead z transkrypcji"""
        lead_data = {
            "name": f"Lead z transkrypcji {transcription_data.get('email_id', '')}",
            "description": transcription_data.get("transcript", ""),
            "source": "transcription",
            "priority": self._determine_priority(transcription_data),
            "metadata": transcription_data
        }
        
        return await self.send_request("create_lead", lead_data)
    
    def _determine_priority(self, transcription_data: Dict[str, Any]) -> str:
        """Określ priorytet na podstawie transkrypcji"""
        transcript = transcription_data.get("transcript", "").lower()
        
        urgent_keywords = ["pilne", "awaria", "nie działa", "emergency", "urgent"]
        high_keywords = ["problem", "naprawa", "serwis", "repair"]
        
        if any(keyword in transcript for keyword in urgent_keywords):
            return "urgent"
        elif any(keyword in transcript for keyword in high_keywords):
            return "high"
        else:
            return "normal"
    
    async def get_metrics(self) -> Dict[str, Any]:
        """📊 Pobierz metryki gateway"""
        return {
            **self.metrics,
            "circuit_breakers": {
                name: {
                    "state": breaker.state,
                    "failure_count": breaker.failure_count
                }
                for name, breaker in self.circuit_breakers.items()
            },
            "services_count": len(self.services),
            "timestamp": datetime.now().isoformat()
        }
    
    async def close(self):
        """🔒 Zamknij połączenia"""
        if self.session:
            await self.session.close()
        
        if self.redis:
            await self.redis.close()
        
        for channel in self.grpc_channels.values():
            await channel.close()
        
        logger.info("🔒 Unified Communication Gateway zamknięty")

# Singleton instance
_gateway_instance: Optional[UnifiedCommunicationGateway] = None

async def get_gateway() -> UnifiedCommunicationGateway:
    """Pobierz singleton instance gateway"""
    global _gateway_instance
    
    if _gateway_instance is None:
        _gateway_instance = UnifiedCommunicationGateway()
        await _gateway_instance.initialize()
    
    return _gateway_instance
