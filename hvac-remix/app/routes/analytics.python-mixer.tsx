import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from "~/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { ExclamationTriangleIcon } from "@radix-ui/react-icons";

export const meta: MetaFunction = () => {
  return [{ title: "Python Mixer Analytics | HVAC CRM" }];
};

// Placeholder for analytical data structure (should match the API route)
interface PythonMixerAnalyticsData {
  totalProcessedEmails: number;
  sentimentDistribution: { positive: number; negative: number; neutral: number };
  emailVolumeOverTime: { date: string; count: number }[];
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const timeframe = url.searchParams.get("timeframe") || "last7days";

  try {
    const response = await fetch(`/api/python-mixer.analytics?timeframe=${timeframe}`);
    const data = await response.json();

    if (!response.ok) {
      return json({ error: data.error || "Failed to load analytics data" }, { status: response.status });
    }

    return json({ analytics: data.data as PythonMixerAnalyticsData });
  } catch (error: any) {
    console.error("Error fetching Python Mixer analytics:", error);
    return json({ error: "Failed to load analytics data", details: error.message }, { status: 500 });
  }
};


export default function PythonMixerAnalyticsPage() {
  const loaderData = useLoaderData<typeof loader>();
  const analytics = 'analytics' in loaderData ? loaderData.analytics : undefined;
  const error = 'error' in loaderData ? loaderData.error : undefined;

  if (error) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertTitle>Błąd ładowania danych analitycznych</AlertTitle>
          <AlertDescription>
            {error.message} {error.details && `(${error.details})`}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="p-4">
        <Alert>
          <AlertTitle>Brak danych</AlertTitle>
          <AlertDescription>
            Brak danych analitycznych do wyświetlenia.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-6">
      <h1 className="text-3xl font-bold">Analizy Python Mixer</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Całkowita liczba przetworzonych e-maili</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-4xl font-bold">{analytics.totalProcessedEmails}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Rozkład sentymentu</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc list-inside">
              <li>Pozytywny: {analytics.sentimentDistribution.positive}%</li>
              <li>Negatywny: {analytics.sentimentDistribution.negative}%</li>
              <li>Neutralny: {analytics.sentimentDistribution.neutral}%</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Wolumen e-maili w czasie</CardTitle>
          </CardHeader>
          <CardContent>
            {analytics.emailVolumeOverTime.length > 0 ? (
              <ul className="list-disc list-inside">
                {analytics.emailVolumeOverTime.map((item: { date: string; count: number }, index: number) => (
                  <li key={index}>{item.date}: {item.count}</li>
                ))}
              </ul>
            ) : (
              <p>Brak danych o wolumenie e-maili.</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Add more sections for other analytical data as needed */}
    </div>
  );
}