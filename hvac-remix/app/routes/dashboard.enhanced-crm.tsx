/**
 * 🚀 ENHANCED CRM DASHBOARD
 *
 * Advanced CRM dashboard combining Customer Lifecycle Management
 * and Service Excellence features in one powerful interface.
 *
 * Philosophy: "The dashboard is the window to the soul of our business"
 */

import { json, type LoaderFunctionArgs, type MetaFunction } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import {
  Users,
  Target,
  TrendingUp,
  Award,
  Heart,
  Wrench,
  BarChart3,
  Settings,
  RefreshCw,
  Download,
  Calendar,
  AlertTriangle
} from "lucide-react";
import { useEffect, useState } from "react";
import { CustomerLifecycleDashboard } from "~/components/organisms/CustomerLifecycleDashboard";
import { CustomerProfileDashboard } from "~/components/organisms/CustomerProfileDashboard";
import { EquipmentRegistryDashboard } from "~/components/organisms/EquipmentRegistryDashboard";
import { ServiceExcellenceDashboard } from "~/components/organisms/ServiceExcellenceDashboard";
import { Badge } from "~/components/ui/badge";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "~/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert"; // Import Alert components
import { ExclamationTriangleIcon } from "@radix-ui/react-icons"; // Import icon for error
import type { DoloresAnalyticsResponse, SentimentData } from "~/routes/api.dolores.analytics";

export const meta: MetaFunction = () => {
  return [
    { title: "Enhanced CRM Dashboard - HVAC Servicetool" },
    { name: "description", content: "Advanced customer lifecycle and service excellence management dashboard" },
  ];
};

interface DashboardMetrics {
  customerLifecycle: {
    totalCustomers: number;
    averageHealthScore: number;
    atRiskCustomers: number;
    loyalCustomers: number;
    churnRate: number;
  };
  serviceExcellence: {
    slaComplianceRate: number;
    averageResponseTime: number;
    customerSatisfaction: number;
    firstTimeFixRate: number;
    criticalBreaches: number;
  };
  businessMetrics: {
    totalRevenue: number;
    monthlyGrowth: number;
    activeContracts: number;
    pendingOrders: number;
  };
  // Add Dolores analytics data
  doloresAnalytics: DoloresAnalyticsResponse;
}

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // In a real implementation, these would be actual API calls
    // For now, providing realistic sample data
    const initialDashboardMetrics: DashboardMetrics = {
      customerLifecycle: {
        totalCustomers: 1247,
        averageHealthScore: 82,
        atRiskCustomers: 23,
        loyalCustomers: 156,
        churnRate: 3.2
      },
      serviceExcellence: {
        slaComplianceRate: 94.5,
        averageResponseTime: 3.2,
        customerSatisfaction: 4.6,
        firstTimeFixRate: 87.3,
        criticalBreaches: 2
      },
      businessMetrics: {
        totalRevenue: 2847650,
        monthlyGrowth: 12.5,
        activeContracts: 89,
        pendingOrders: 34
      },
      doloresAnalytics: { // Initial mock data for Dolores
        totalEmails: 0,
        transcriptionCount: 0,
        averageScore: 0,
        topCategories: [],
        trendData: [],
        sentimentDistribution: [],
        priorityBreakdown: [],
        customerEnrichment: {
          profilesEnriched: 0,
          newContacts: 0,
          updatedPreferences: 0,
          churnRiskIdentified: 0
        },
        workflowTriggers: {
          triggersActivated: 0,
          ticketsCreated: 0,
          escalationsTriggered: 0,
          followUpsScheduled: 0
        },
        businessInsights: {
          leadScore: 0,
          conversionProbability: 0,
          estimatedRevenue: 0,
          customerSatisfaction: 0
        }
      }
    };

    return json({ metrics: initialDashboardMetrics });
  } catch (error: any) {
    console.error('Error loading dashboard data:', error);
    return json(
      { error: 'Failed to load dashboard data', details: error.message },
      { status: 500 }
    );
  }
}

export default function EnhancedCRMDashboard() {
  const loaderData = useLoaderData<typeof loader>();

  // Handle loader errors
  if ('error' in loaderData) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertTitle>Błąd ładowania danych pulpitu</AlertTitle>
          <AlertDescription>
            {(loaderData as { error: string; details?: string }).error} {(loaderData as { error: string; details?: string }).details && `(${(loaderData as { error: string; details?: string }).details})`}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Handle loader errors
  if ('error' in loaderData) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertTitle>Błąd ładowania danych pulpitu</AlertTitle>
          <AlertDescription>
            {loaderData.error} {loaderData.details && `(${loaderData.details})`}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const [metrics, setMetrics] = useState<DashboardMetrics>(loaderData.metrics);

  useEffect(() => {
    const eventSource = new EventSource("/api/dolores.analytics/websocket");

    eventSource.onmessage = (event) => {
      try {
        const newAnalyticsData: DoloresAnalyticsResponse = JSON.parse(event.data);
        setMetrics(prevMetrics => ({
          ...prevMetrics,
          doloresAnalytics: newAnalyticsData
        }));
      } catch (parseError) {
        console.error("Error parsing SSE data:", parseError);
      }
    };

    eventSource.onerror = (error) => {
      console.error("EventSource failed:", error);
      eventSource.close();
    };

    return () => {
      eventSource.close();
    };
  }, []);

  const getHealthScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getSLAComplianceColor = (rate: number): string => {
    if (rate >= 95) return 'text-green-600';
    if (rate >= 85) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Enhanced CRM Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Advanced customer lifecycle and service excellence management
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Real-time
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Export Report
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Customers</p>
                <p className="text-2xl font-bold">{metrics.customerLifecycle.totalCustomers.toLocaleString()}</p>
                <p className="text-sm text-green-600 mt-1">
                  +{metrics.businessMetrics.monthlyGrowth}% this month
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Health Score</p>
                <p className={`text-2xl font-bold ${getHealthScoreColor(metrics.customerLifecycle.averageHealthScore)}`}>
                  {metrics.customerLifecycle.averageHealthScore}%
                </p>
                <p className="text-sm text-gray-600 mt-1">Customer health</p>
              </div>
              <Heart className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-yellow-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">SLA Compliance</p>
                <p className={`text-2xl font-bold ${getSLAComplianceColor(metrics.serviceExcellence.slaComplianceRate)}`}>
                  {metrics.serviceExcellence.slaComplianceRate}%
                </p>
                <p className="text-sm text-gray-600 mt-1">Service level</p>
              </div>
              <Target className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-green-600">
                  ${(metrics.businessMetrics.totalRevenue / 1000000).toFixed(1)}M
                </p>
                <p className="text-sm text-green-600 mt-1">
                  +{metrics.businessMetrics.monthlyGrowth}% growth
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alert Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {metrics.customerLifecycle.atRiskCustomers > 0 && (
          <Card className="border-l-4 border-l-red-500">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                <div>
                  <p className="font-medium text-red-800">
                    {metrics.customerLifecycle.atRiskCustomers} At-Risk Customers
                  </p>
                  <p className="text-sm text-red-600">Require immediate attention</p>
                </div>
                <Button size="sm" variant="outline" className="ml-auto">
                  Review
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {metrics.serviceExcellence.criticalBreaches > 0 && (
          <Card className="border-l-4 border-l-orange-500">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="font-medium text-orange-800">
                    {metrics.serviceExcellence.criticalBreaches} SLA Breaches
                  </p>
                  <p className="text-sm text-orange-600">Critical priority orders</p>
                </div>
                <Button size="sm" variant="outline" className="ml-auto">
                  Fix Now
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {metrics.businessMetrics.pendingOrders > 0 && (
          <Card className="border-l-4 border-l-blue-500">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-800">
                    {metrics.businessMetrics.pendingOrders} Pending Orders
                  </p>
                  <p className="text-sm text-blue-600">Awaiting scheduling</p>
                </div>
                <Button size="sm" variant="outline" className="ml-auto">
                  Schedule
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Main Dashboard Tabs */}
      <Tabs defaultValue="customer-lifecycle" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="customer-lifecycle" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Customer Lifecycle
          </TabsTrigger>
          <TabsTrigger value="service-excellence" className="flex items-center gap-2">
            <Award className="h-4 w-4" />
            Service Excellence
          </TabsTrigger>
          <TabsTrigger value="customer-profiles" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Customer Profiles
          </TabsTrigger>
          <TabsTrigger value="equipment-registry" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Equipment Registry
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="customer-lifecycle" className="mt-6">
          <CustomerLifecycleDashboard showOverview={true} />
        </TabsContent>

        <TabsContent value="service-excellence" className="mt-6">
          <ServiceExcellenceDashboard timeRange="month" />
        </TabsContent>

        <TabsContent value="customer-profiles" className="mt-6">
          <CustomerProfileDashboard
            customerId="sample_customer_1"
            onActionClick={(action, data) => {
              console.log('Customer profile action:', action, data);
              // Handle customer profile actions
            }}
          />
        </TabsContent>

        <TabsContent value="equipment-registry" className="mt-6">
          <EquipmentRegistryDashboard
            customerId="sample_customer_1"
            onActionClick={(action, data) => {
              console.log('Equipment action:', action, data);
              // Handle equipment actions
            }}
          />
        </TabsContent>

        <TabsContent value="analytics" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Real-time Dolores Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Email Processing Insights</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Total Processed Emails:</span>
                      <Badge variant="outline">{metrics.doloresAnalytics.totalEmails}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Transcription Count:</span>
                      <Badge variant="outline">{metrics.doloresAnalytics.transcriptionCount}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Average Sentiment Score:</span>
                      <Badge variant="outline">{metrics.doloresAnalytics.averageScore}</Badge>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Sentiment Distribution</h3>
                  <div className="space-y-2">
                    {metrics.doloresAnalytics.sentimentDistribution.map((s: SentimentData, index: number) => (
                      <div key={index} className="flex justify-between">
                        <span>{s.sentiment}:</span>
                        <Badge variant="outline">{s.percentage}% ({s.count})</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="mt-6 pt-6 border-t">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Workflow & Business Insights</h3>
                  <div className="flex gap-2">
                    <div className="flex justify-between w-full">
                      <span>Triggers Activated:</span>
                      <Badge variant="outline">{metrics.doloresAnalytics.workflowTriggers.triggersActivated}</Badge>
                    </div>
                    <div className="flex justify-between w-full">
                      <span>Tickets Created:</span>
                      <Badge variant="outline">{metrics.doloresAnalytics.workflowTriggers.ticketsCreated}</Badge>
                    </div>
                  </div>
                  <div className="flex gap-2 mt-2">
                    <div className="flex justify-between w-full">
                      <span>Estimated Revenue:</span>
                      <Badge variant="outline">${metrics.doloresAnalytics.businessInsights.estimatedRevenue.toLocaleString()}</Badge>
                    </div>
                    <div className="flex justify-between w-full">
                      <span>Lead Score:</span>
                      <Badge variant="outline">{metrics.doloresAnalytics.businessInsights.leadScore}</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
