import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { ExclamationTriangleIcon } from "@radix-ui/react-icons";
import type { GoSpineCustomerData } from "~/routes/api.go-spine.customers"; // Import the interface

export const meta: MetaFunction = () => {
  return [{ title: "GoSpine Customers | HVAC CRM" }];
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const customerId = url.searchParams.get("customerId");

  try {
    const response = await fetch(`/api/go-spine.customers${customerId ? `?customerId=${customerId}` : ""}`);
    const data = await response.json();

    if (!response.ok) {
      return json({ error: data.error || "Failed to load customer data" }, { status: response.status });
    }

    return json({ customers: data.data as GoSpineCustomerData[] });
  } catch (error: any) {
    console.error("Error fetching GoSpine customer data:", error);
    return json({ error: "Failed to load customer data", details: error.message }, { status: 500 });
  }
};

export default function GoSpineCustomersPage() {
  const loaderData = useLoaderData<typeof loader>();
  const customers = 'customers' in loaderData ? loaderData.customers : undefined;
  const error = 'error' in loaderData ? loaderData.error : undefined;

  if (error) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertTitle>Błąd ładowania danych klientów</AlertTitle>
          <AlertDescription>
            {error.message} {error.details && `(${error.details})`}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!customers || customers.length === 0) {
    return (
      <div className="p-4">
        <Alert>
          <AlertTitle>Brak danych</AlertTitle>
          <AlertDescription>
            Brak danych klientów do wyświetlenia z GoSpine.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-6">
      <h1 className="text-3xl font-bold">Klienci GoSpine</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {customers.map((customer) => (
          <Card key={customer.id}>
            <CardHeader>
              <CardTitle>{customer.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Email: {customer.email}</p>
              <p>Telefon: {customer.phone}</p>
              <p>Adres: {customer.address}</p>
              {customer.serviceHistory && customer.serviceHistory.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mt-4">Historia Serwisu:</h3>
                  <ul>
                    {customer.serviceHistory.map((service) => (
                      <li key={service.serviceId}>
                        {service.date} - {service.description} ({service.status})
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}