import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";

// Placeholder for GoSpine API URL
// TODO: Replace with actual GoSpine API URL
const GOSPINE_API_URL = process.env['GOSPINE_API_URL'] || "http://localhost:8080/api/customers";

// Placeholder for GoSpine customer data structure
export interface GoSpineCustomerData {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  serviceHistory: {
    serviceId: string;
    date: string;
    description: string;
    status: string;
  }[];
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const url = new URL(request.url);
    const customerId = url.searchParams.get("customerId"); // Example: fetch a specific customer

    let goSpineApiUrl = GOSPINE_API_URL;
    if (customerId) {
      goSpineApiUrl = `${GOSPINE_API_URL}/${customerId}`;
    }

    // Fetch data from GoSpine backend
    const response = await fetch(goSpineApiUrl);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error fetching GoSpine customer data: ${response.status} - ${errorText}`);
      return json(
        { error: `Failed to fetch customer data from GoSpine: ${response.statusText}` },
        { status: response.status }
      );
    }

    const customerData: GoSpineCustomerData | GoSpineCustomerData[] = await response.json();

    return json({ success: true, data: customerData });
  } catch (error: any) {
    console.error("Error in GoSpine customer API route:", error);
    return json(
      { error: "Internal server error while fetching GoSpine customer data", details: error.message },
      { status: 500 }
    );
  }
};