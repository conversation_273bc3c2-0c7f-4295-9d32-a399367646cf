FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js for MCP tools
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Install Playwright dependencies
RUN apt-get update && apt-get install -y \
    libnss3 \
    libnspr4 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
COPY requirements-enhanced.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r requirements-enhanced.txt

# Install Playwright browsers
RUN playwright install chromium

# Install MCP tools globally
RUN npm install -g @modelcontextprotocol/server-memory
RUN npm install -g @upstash/context7-mcp
RUN npm install -g @wonderwhy-er/desktop-commander
RUN npm install -g tavily-mcp@0.2.1

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/chroma_db

# Set permissions
RUN chmod +x /app/*.py

# Expose ports
EXPOSE 8501 8978 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8978/health || exit 1

# Default command
CMD ["python", "mcp_server.py"]
