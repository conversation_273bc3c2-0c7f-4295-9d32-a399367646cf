# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
MODEL_CHOICE=gpt-4o-mini

# Local LM Studio Configuration
LM_STUDIO_URL=http://*************:1234

# Tavily Search API
TAVILY_API_KEY=your_tavily_api_key_here

# Firecrawl API (optional)
FIRECRAWL_API_KEY=your_firecrawl_api_key_here

# Database Configuration
CHROMA_DB_HOST=chromadb
CHROMA_DB_PORT=8000
REDIS_HOST=redis
REDIS_PORT=6379

# MCP Server Configuration
MCP_SERVER_PORT=8978

# Workspace Configuration
WORKSPACE_ROOT=/workspace
INDEXING_INTERVAL=300

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Security Configuration
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Performance Configuration
MAX_CONCURRENT_CRAWLS=10
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_FILE_SIZE=1048576

# MinIO Configuration (for HVAC project integration)
MINIO_ENDPOINT=**************:9000
MINIO_ACCESS_KEY=koldbringer
MINIO_SECRET_KEY=Blaeritipol1
MINIO_BUCKET=hvac-documents

# MongoDB Configuration (for HVAC project integration)
MONGODB_URL=***************************************************************

# PostgreSQL Configuration (for HVAC project integration)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=hvac_crm
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_postgres_password