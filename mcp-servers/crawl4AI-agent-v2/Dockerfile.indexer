FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
COPY requirements-enhanced.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r requirements-enhanced.txt

# Copy workspace indexer code
COPY workspace_indexer.py .
COPY utils.py .

# Create necessary directories
RUN mkdir -p /app/logs

# Set permissions
RUN chmod +x /app/*.py

# Health check
HEALTHCHECK --interval=60s --timeout=10s --start-period=10s --retries=3 \
    CMD python -c "import redis; r=redis.Redis(host='redis'); r.ping()" || exit 1

# Default command
CMD ["python", "workspace_indexer.py"]
