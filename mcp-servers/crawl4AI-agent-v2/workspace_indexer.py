#!/usr/bin/env python3
"""
Workspace Indexer Service for Crawl4AI Enhanced MCP Server
Continuously monitors and indexes workspace files for semantic search
"""

import asyncio
import hashlib
import json
import logging
import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Set
import mimetypes

import chromadb
import redis
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import structlog
from sentence_transformers import SentenceTransformer

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

class WorkspaceIndexer:
    """Workspace file indexer with semantic search capabilities"""
    
    def __init__(self):
        self.workspace_root = Path(os.getenv("WORKSPACE_ROOT", "/workspace"))
        self.indexing_interval = int(os.getenv("INDEXING_INTERVAL", "300"))  # 5 minutes
        
        # Setup connections
        self.setup_connections()
        
        # Initialize embedding model
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # File extensions to index
        self.indexable_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.go', '.rs', '.java', '.cpp', '.c', '.h',
            '.md', '.txt', '.rst', '.yaml', '.yml', '.json', '.xml', '.html', '.css',
            '.sql', '.sh', '.bash', '.dockerfile', '.env', '.gitignore', '.toml', '.ini'
        }
        
        # Files to ignore
        self.ignore_patterns = {
            '__pycache__', '.git', 'node_modules', '.venv', 'venv', '.env',
            'dist', 'build', '.next', '.nuxt', 'target', 'bin', 'obj'
        }
        
        logger.info("Workspace indexer initialized", 
                   workspace_root=str(self.workspace_root),
                   indexing_interval=self.indexing_interval)
    
    def setup_connections(self):
        """Setup database connections"""
        try:
            # ChromaDB connection
            chroma_host = os.getenv("CHROMA_DB_HOST", "localhost")
            chroma_port = int(os.getenv("CHROMA_DB_PORT", "8000"))
            self.chroma_client = chromadb.HttpClient(
                host=chroma_host,
                port=chroma_port
            )
            
            # Get or create workspace collection
            try:
                self.collection = self.chroma_client.get_collection("workspace")
            except:
                self.collection = self.chroma_client.create_collection(
                    name="workspace",
                    metadata={"description": "Workspace files semantic index"}
                )
            
            # Redis connection for caching and coordination
            redis_host = os.getenv("REDIS_HOST", "localhost")
            redis_port = int(os.getenv("REDIS_PORT", "6379"))
            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                decode_responses=True
            )
            
            logger.info("Database connections established")
            
        except Exception as e:
            logger.error("Failed to setup database connections", error=str(e))
            raise
    
    def should_index_file(self, file_path: Path) -> bool:
        """Check if file should be indexed"""
        # Check if file exists and is readable
        if not file_path.is_file() or not os.access(file_path, os.R_OK):
            return False
        
        # Check file extension
        if file_path.suffix.lower() not in self.indexable_extensions:
            return False
        
        # Check ignore patterns
        for part in file_path.parts:
            if part in self.ignore_patterns:
                return False
        
        # Check file size (skip very large files)
        try:
            if file_path.stat().st_size > 1024 * 1024:  # 1MB limit
                return False
        except OSError:
            return False
        
        return True
    
    def get_file_hash(self, file_path: Path) -> str:
        """Get file content hash for change detection"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                return hashlib.md5(content).hexdigest()
        except Exception:
            return ""
    
    def extract_file_content(self, file_path: Path) -> Optional[str]:
        """Extract text content from file"""
        try:
            # Try to read as text with different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                        return content
                except UnicodeDecodeError:
                    continue
            
            logger.warning("Could not decode file", file_path=str(file_path))
            return None
            
        except Exception as e:
            logger.error("Failed to extract file content", 
                        file_path=str(file_path), error=str(e))
            return None
    
    def chunk_content(self, content: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """Split content into overlapping chunks"""
        if len(content) <= chunk_size:
            return [content]
        
        chunks = []
        start = 0
        
        while start < len(content):
            end = start + chunk_size
            chunk = content[start:end]
            
            # Try to break at word boundary
            if end < len(content):
                last_space = chunk.rfind(' ')
                if last_space > chunk_size // 2:
                    chunk = chunk[:last_space]
                    end = start + last_space
            
            chunks.append(chunk.strip())
            start = end - overlap
            
            if start >= len(content):
                break
        
        return chunks
    
    async def index_file(self, file_path: Path) -> bool:
        """Index a single file"""
        try:
            if not self.should_index_file(file_path):
                return False
            
            # Get relative path from workspace root
            try:
                rel_path = file_path.relative_to(self.workspace_root)
            except ValueError:
                # File is outside workspace
                return False
            
            # Check if file has changed
            current_hash = self.get_file_hash(file_path)
            cached_hash = self.redis_client.get(f"file_hash:{rel_path}")
            
            if cached_hash == current_hash:
                logger.debug("File unchanged, skipping", file_path=str(rel_path))
                return True
            
            # Extract content
            content = self.extract_file_content(file_path)
            if not content:
                return False
            
            # Chunk content
            chunks = self.chunk_content(content)
            
            # Remove existing entries for this file
            try:
                self.collection.delete(where={"file_path": str(rel_path)})
            except Exception:
                pass  # Collection might be empty
            
            # Index chunks
            documents = []
            metadatas = []
            ids = []
            
            for i, chunk in enumerate(chunks):
                doc_id = f"{rel_path}#{i}"
                
                metadata = {
                    "file_path": str(rel_path),
                    "file_name": file_path.name,
                    "file_type": file_path.suffix.lower(),
                    "chunk_index": i,
                    "total_chunks": len(chunks),
                    "file_size": file_path.stat().st_size,
                    "modified_time": file_path.stat().st_mtime,
                    "mime_type": mimetypes.guess_type(str(file_path))[0] or "text/plain"
                }
                
                documents.append(chunk)
                metadatas.append(metadata)
                ids.append(doc_id)
            
            # Add to ChromaDB
            self.collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            
            # Update hash cache
            self.redis_client.set(f"file_hash:{rel_path}", current_hash)
            
            logger.info("File indexed successfully", 
                       file_path=str(rel_path), 
                       chunks=len(chunks))
            
            return True
            
        except Exception as e:
            logger.error("Failed to index file", 
                        file_path=str(file_path), error=str(e))
            return False
    
    async def index_workspace(self):
        """Index entire workspace"""
        logger.info("Starting workspace indexing", workspace_root=str(self.workspace_root))
        
        indexed_count = 0
        error_count = 0
        
        for file_path in self.workspace_root.rglob("*"):
            if file_path.is_file():
                try:
                    success = await self.index_file(file_path)
                    if success:
                        indexed_count += 1
                    else:
                        error_count += 1
                except Exception as e:
                    logger.error("Error indexing file", 
                                file_path=str(file_path), error=str(e))
                    error_count += 1
        
        logger.info("Workspace indexing completed", 
                   indexed=indexed_count, 
                   errors=error_count)
        
        # Update indexing timestamp
        self.redis_client.set("last_indexing", int(time.time()))
    
    async def run_periodic_indexing(self):
        """Run periodic indexing"""
        while True:
            try:
                await self.index_workspace()
                await asyncio.sleep(self.indexing_interval)
            except Exception as e:
                logger.error("Periodic indexing failed", error=str(e))
                await asyncio.sleep(60)  # Wait 1 minute before retry

class WorkspaceFileHandler(FileSystemEventHandler):
    """File system event handler for real-time indexing"""
    
    def __init__(self, indexer: WorkspaceIndexer):
        self.indexer = indexer
        self.pending_files: Set[Path] = set()
        self.last_event_time = {}
        
    def on_modified(self, event):
        if not event.is_directory:
            file_path = Path(event.src_path)
            
            # Debounce rapid file changes
            now = time.time()
            if file_path in self.last_event_time:
                if now - self.last_event_time[file_path] < 1.0:  # 1 second debounce
                    return
            
            self.last_event_time[file_path] = now
            self.pending_files.add(file_path)
    
    def on_created(self, event):
        self.on_modified(event)
    
    async def process_pending_files(self):
        """Process pending file changes"""
        while True:
            if self.pending_files:
                files_to_process = list(self.pending_files)
                self.pending_files.clear()
                
                for file_path in files_to_process:
                    try:
                        await self.indexer.index_file(file_path)
                    except Exception as e:
                        logger.error("Failed to process file change", 
                                   file_path=str(file_path), error=str(e))
            
            await asyncio.sleep(5)  # Check every 5 seconds

async def main():
    """Main entry point"""
    indexer = WorkspaceIndexer()
    
    # Setup file system watcher
    event_handler = WorkspaceFileHandler(indexer)
    observer = Observer()
    observer.schedule(event_handler, str(indexer.workspace_root), recursive=True)
    observer.start()
    
    logger.info("Workspace indexer started")
    
    try:
        # Run initial indexing
        await indexer.index_workspace()
        
        # Start background tasks
        await asyncio.gather(
            indexer.run_periodic_indexing(),
            event_handler.process_pending_files()
        )
    except KeyboardInterrupt:
        logger.info("Shutting down workspace indexer")
    finally:
        observer.stop()
        observer.join()

if __name__ == "__main__":
    asyncio.run(main())
