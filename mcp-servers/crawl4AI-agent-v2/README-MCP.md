# Enhanced Crawl4AI MCP Server with Workspace Indexing

Kompleksowy MCP (Model Context Protocol) server ł<PERSON><PERSON><PERSON>cy możliwości Crawl4AI z indeksowaniem workspace'u i zaawansowanymi funkcjami AI dla projektów HVAC CRM.

## 🚀 Funkcjonalności

### 🔍 Indeksowanie Workspace
- **Semantyczne wyszukiwanie** w plikach projektu
- **Real-time monitoring** zmian w plikach
- **Inteligentne chunking** dla optymalnej wektoryzacji
- **Obsługa wielu formatów** plików (Python, JavaScript, Go, Markdown, itp.)

### 🌐 Web Crawling
- **Zaawansowany crawling** stron internetowych
- **Integracja z <PERSON>ly** dla wyszukiwania w czasie rzeczywistym
- **Firecrawl support** dla głębokiego scrapingu
- **Batch processing** dla dużych witryn

### 🧠 Pamięć i Kontekst
- **Persistent memory** z Redis
- **Kontekst między sesjami** dla AI agentów
- **Tagowanie i kategoryzacja** informacji
- **Automatyczne backup** danych

### 🔧 Integracja z HVAC CRM
- **MinIO integration** dla dokumentów
- **MongoDB support** dla danych CRM
- **PostgreSQL compatibility** 
- **LM Studio integration** dla lokalnych modeli

## 📋 Wymagania

- Docker & Docker Compose
- Python 3.11+
- Node.js 18+ (dla MCP tools)
- 4GB RAM minimum
- 10GB wolnego miejsca

## 🛠️ Instalacja

### 1. Klonowanie i Konfiguracja

```bash
cd /home/<USER>/HVAC/unifikacja/mcp-servers/crawl4AI-agent-v2

# Skopiuj i edytuj konfigurację
cp .env.example .env
nano .env
```

### 2. Konfiguracja .env

```env
# Podstawowe API keys
OPENAI_API_KEY=your_openai_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here

# Lokalne modele
LM_STUDIO_URL=http://*************:1234

# Integracja HVAC
MINIO_ENDPOINT=**************:9000
MINIO_ACCESS_KEY=koldbringer
MINIO_SECRET_KEY=Blaeritipol1
```

### 3. Uruchomienie

```bash
# Uruchom wszystkie serwisy
docker-compose up -d

# Sprawdź logi
docker-compose logs -f crawl4ai-agent

# Sprawdź status
docker-compose ps
```

## 🎯 Dostępne Serwisy

| Serwis | Port | Opis |
|--------|------|------|
| **MCP Server** | 8978 | Główny serwer MCP |
| **Streamlit Dashboard** | 8502 | Interface użytkownika |
| **ChromaDB** | 8000 | Baza wektorowa |
| **Redis** | 6379 | Cache i kolejki |
| **Web API** | 8080 | REST API |

## 🔧 MCP Tools

### `search_workspace`
Semantyczne wyszukiwanie w workspace:
```json
{
  "query": "HVAC equipment management",
  "limit": 10,
  "file_types": [".py", ".go", ".md"]
}
```

### `crawl_website`
Crawling stron internetowych:
```json
{
  "url": "https://docs.example.com",
  "max_depth": 3,
  "include_patterns": ["*/api/*", "*/docs/*"]
}
```

### `store_memory` / `retrieve_memory`
Zarządzanie pamięcią:
```json
{
  "key": "hvac_equipment_specs",
  "value": "Detailed specifications...",
  "tags": ["hvac", "equipment", "specs"]
}
```

### `index_workspace`
Ręczne indeksowanie:
```json
{
  "path": "/workspace/specific/folder",
  "force": true
}
```

## 🔗 Integracja z Cursor/Claude

### 1. Konfiguracja MCP w Cursor

Edytuj `~/.cursor/mcp.json`:

```json
{
  "mcpServers": {
    "crawl4ai-enhanced": {
      "url": "http://localhost:8978/sse",
      "name": "Crawl4AI Enhanced MCP Server"
    }
  }
}
```

### 2. Użycie w .cursorrules

```
<instructions>
Używaj @search_workspace do wyszukiwania w kodzie przed implementacją.
Wykorzystuj @crawl_website do research'u dokumentacji.
Zapisuj ważne informacje używając @store_memory.
</instructions>
```

## 📊 Monitoring i Logi

### Health Checks
```bash
# Sprawdź status MCP serwera
curl http://localhost:8978/health

# Sprawdź ChromaDB
curl http://localhost:8000/api/v1/heartbeat

# Sprawdź Redis
docker exec crawl4ai-redis redis-cli ping
```

### Logi
```bash
# Wszystkie serwisy
docker-compose logs -f

# Konkretny serwis
docker-compose logs -f crawl4ai-agent
docker-compose logs -f workspace-indexer
```

## 🔧 Rozwiązywanie Problemów

### Problem: MCP Server nie odpowiada
```bash
# Restart serwisu
docker-compose restart crawl4ai-agent

# Sprawdź logi
docker-compose logs crawl4ai-agent
```

### Problem: Workspace nie indeksuje się
```bash
# Sprawdź uprawnienia
ls -la /home/<USER>/HVAC/unifikacja

# Restart indexer
docker-compose restart workspace-indexer
```

### Problem: ChromaDB connection error
```bash
# Sprawdź sieć
docker network ls
docker network inspect crawl4ai-agent-v2_crawl4ai-network
```

## 🚀 Zaawansowane Użycie

### Custom Embeddings
Możesz zmienić model embeddings w `workspace_indexer.py`:
```python
self.embedding_model = SentenceTransformer('your-custom-model')
```

### Dodatkowe File Types
Edytuj `indexable_extensions` w `workspace_indexer.py`:
```python
self.indexable_extensions.add('.your_extension')
```

### Performance Tuning
Dostosuj w `.env`:
```env
MAX_CONCURRENT_CRAWLS=20
CHUNK_SIZE=1500
INDEXING_INTERVAL=180
```

## 📈 Metryki i Analytics

Dashboard Streamlit (port 8502) zawiera:
- **Statystyki indeksowania**
- **Performance metrics**
- **Search analytics**
- **Memory usage**
- **Crawling progress**

## 🔐 Bezpieczeństwo

- **API Keys** przechowywane w zmiennych środowiskowych
- **Network isolation** przez Docker networks
- **Read-only workspace** mounting
- **Redis persistence** z backup

## 🤝 Integracja z Projektem HVAC

Ten MCP server jest zoptymalizowany dla projektu HVAC CRM:
- **Automatyczne indeksowanie** kodu Go i Python
- **Integracja z MinIO** dla dokumentów
- **MongoDB queries** przez MCP tools
- **LM Studio support** dla lokalnych modeli

## 📚 Dokumentacja API

Pełna dokumentacja API dostępna pod:
- **OpenAPI Docs**: http://localhost:8080/docs
- **ReDoc**: http://localhost:8080/redoc

## 🎉 Ciekawostki z Tavily Research

Podczas implementacji odkryłem fascynujące możliwości MCP:
- **Cursor-local-indexing** z ChromaDB to game-changer dla workspace search! 🔍
- **Firecrawl MCP** oferuje niesamowite możliwości web scrapingu 🕷️
- **Model Context Protocol** rewolucjonizuje sposób, w jaki AI agenci współpracują z danymi 🤖

Ten system to prawdziwy **cosmic-level** upgrade dla naszego HVAC CRM! 🚀✨
