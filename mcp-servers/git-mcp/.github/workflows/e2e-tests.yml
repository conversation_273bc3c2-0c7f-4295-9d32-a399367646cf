name: E2E Tests

on:
  pull_request:
    paths:
      - 'src/**'
      - 'tests/e2e/**'
      - 'package.json'
      - 'pnpm-lock.yaml'
      - 'wrangler.jsonc'
      - '.github/workflows/e2e-tests.yml'

jobs:
  e2e_test:
    runs-on: ubuntu-latest
    timeout-minutes: 15 # Adjust timeout as needed
    permissions:
      contents: read
      pull-requests: write # Required to post the preview URL comment back to the PR

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20' # Match your project's Node.js version requirement

      - name: Setup PNPM
        uses: pnpm/action-setup@v4

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install Playwright Browsers
        run: npx playwright install chromium --with-deps

      - name: Create .dev.vars with test environment
        run: echo "ENVIRONMENT=test" > .dev.vars

      - name: Run Playwright tests
        run: pnpm run test:e2e