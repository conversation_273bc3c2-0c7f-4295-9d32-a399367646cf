{"$schema": "https://biomejs.dev/schemas/1.6.2/schema.json", "organizeImports": {"enabled": true}, "files": {"ignore": ["worker-configuration.d.ts"]}, "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "off", "noDebugger": "off", "noConsoleLog": "off", "noConfusingVoidType": "off"}, "style": {"noNonNullAssertion": "off"}}}, "formatter": {"enabled": true, "indentWidth": 4, "lineWidth": 100}}