{"name": "git-mcp", "version": "1.0.0", "description": "GitMCP is a tool that allows you to get the documentation for a given repository.", "private": true, "type": "module", "scripts": {"build": "react-router build", "deploy": "npm run build && wrangler deploy", "dev": "react-router dev", "preview": "vite preview", "start": "wrangler dev", "format": "biome format --write", "lint:fix": "biome lint --fix", "cf-typegen": "wrangler types", "test": "vitest run", "test:e2e": "playwright test", "prepare": "husky"}, "lint-staged": {"src/**/*.ts": ["prettier --write"], "src/**/*.tsx": ["prettier --write"], "app/**/*.ts": ["prettier --write"], "app/**/*.tsx": ["prettier --write"]}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.7.1+sha512.2d92c86b7928dc8284f53494fb4201f983da65f0fb4f0d40baafa5cf628fa31dae3e5968f12466f17df7e97310e30f343a648baea1b9b350685dafafffdf5808", "dependencies": {"@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/cohere": "^1.2.9", "@ai-sdk/google": "^1.2.13", "@ai-sdk/groq": "^1.2.8", "@ai-sdk/openai": "^1.3.20", "@ai-sdk/react": "^1.2.9", "@ai-sdk/xai": "^1.2.15", "@cloudflare/workers-oauth-provider": "^0.0.2", "@modelcontextprotocol/sdk": "^1.11.2", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.4", "@react-router/fs-routes": "^7.5.2", "@remix-run/cloudflare": "^2.16.5", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "agents": "^0.0.84", "ai": "^4.3.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "fast-deep-equal": "^3.1.3", "hono": "^4.7.7", "html-to-md": "^0.8.7", "isbot": "^5.1.27", "lucide-react": "^0.487.0", "motion": "^12.9.2", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-router": "^7.5.2", "react-router-dom": "^7.5.2", "remark-gfm": "^4.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "typewriter-effect": "^2.21.0", "zod": "^3.24.3"}, "devDependencies": {"@cloudflare/vite-plugin": "^0.1.21", "@cloudflare/workers-types": "^4.20250505.0", "@playwright/test": "^1.52.0", "@react-router/dev": "^7.5.2", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20.17.31", "@types/node-fetch": "^2.6.12", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "husky": "^9.1.7", "lint-staged": "^15.5.1", "marked": "^15.0.11", "node-fetch": "^3.3.2", "prettier": "^3.5.3", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^1.6.1", "workers-mcp": "0.1.0-3", "wrangler": "^4.14.1"}}